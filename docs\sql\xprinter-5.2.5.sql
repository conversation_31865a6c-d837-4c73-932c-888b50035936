-- 微信应用迁移相关数据库脚本
-- 创建时间: 2025年1月11日
-- 用途: 支持微信开放平台应用迁移的unionId批量更新功能
-- 兼容: MySQL 5.7 GTID模式

-- 1. 创建 unionId 迁移记录表
CREATE TABLE unionid_migration_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '触发更新的用户ID',
    login_type TINYINT NOT NULL COMMENT '登录类型：0=QQ，1=微信',
    open_id VARCHAR(255) COMMENT 'openId',
    old_union_id VARCHAR(255) COMMENT '旧的unionId',
    new_union_id VARCHAR(255) COMMENT '新的unionId',
    update_type VARCHAR(20) DEFAULT 'batch_update' COMMENT '更新类型：batch_update=批量更新, manual_update=手动更新',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    ip_address VARCHAR(45) COMMENT '更新时的IP地址',
    
    -- 索引优化
    INDEX idx_user_id (user_id),
    INDEX idx_update_time (update_time),
    INDEX idx_old_union_id (old_union_id),
    INDEX idx_new_union_id (new_union_id),
    INDEX idx_login_type (login_type),
    INDEX idx_update_type (update_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='UnionId迁移记录表';

-- 2. 创建数据备份表（兼容MySQL 5.7 GTID模式）
-- 先创建表结构
CREATE TABLE user_unionid_backup_20250111 (
    userId INT(11) NOT NULL,
    wxId VARCHAR(255) DEFAULT NULL,
    wxUnionId VARCHAR(255) DEFAULT NULL,
    qqId VARCHAR(255) DEFAULT NULL,
    qqUnionId VARCHAR(255) DEFAULT NULL,
    registerType TINYINT(4) DEFAULT NULL,
    userNickName VARCHAR(255) DEFAULT NULL,
    createTime DATETIME DEFAULT NULL,
    backup_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (userId),
    INDEX idx_wx_union_id (wxUnionId),
    INDEX idx_qq_union_id (qqUnionId),
    INDEX idx_backup_time (backup_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户unionId数据备份表';

-- 再插入备份数据
INSERT INTO user_unionid_backup_20250111
(userId, wxId, wxUnionId, qqId, qqUnionId, registerType, userNickName, createTime, backup_time)
SELECT
    userId,
    wxId,
    wxUnionId,
    qqId,
    qqUnionId,
    registerType,
    userNickName,
    createTime,
    NOW()
FROM user
WHERE wxUnionId IS NOT NULL OR qqUnionId IS NOT NULL;

-- 验证备份数据
SELECT
    '备份表记录数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
UNION ALL
SELECT
    '微信用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT
    'QQ用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
WHERE qqUnionId IS NOT NULL;

-- 注意事项：
-- 如果遇到 "Error Code: 1786. Statement violates GTID consistency: CREATE TABLE ... SELECT" 错误
-- 说明MySQL启用了GTID模式，不支持 CREATE TABLE ... AS SELECT 语句
-- 解决方案：使用上面的分步创建方式（先CREATE TABLE，再INSERT SELECT）

-- 3. 监控和统计查询脚本

-- 3.1 批量更新监控查询
-- 查看每日批量更新统计
SELECT
    DATE(update_time) as date,
    login_type,
    old_union_id,
    new_union_id,
    COUNT(DISTINCT user_id) as trigger_users,
    update_type
FROM unionid_migration_log
WHERE update_type = 'batch_update'
GROUP BY DATE(update_time), login_type, old_union_id, new_union_id
ORDER BY date DESC;

-- 3.2 迁移完成度监控
-- 统计迁移进度
SELECT
    '迁移前微信用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT
    '已完成迁移的unionId数量' as metric,
    COUNT(DISTINCT old_union_id) as value
FROM unionid_migration_log
WHERE login_type = 1 AND update_type = 'batch_update';

-- 3.3 查看具体的批量更新情况
SELECT
    old_union_id,
    new_union_id,
    MIN(update_time) as first_update,
    COUNT(DISTINCT user_id) as affected_users
FROM unionid_migration_log
WHERE update_type = 'batch_update' AND login_type = 1
GROUP BY old_union_id, new_union_id
ORDER BY first_update DESC;

-- 3.4 异常检测查询
-- 检查同一用户多次更新unionId的情况
SELECT
    user_id,
    COUNT(*) as update_count,
    GROUP_CONCAT(CONCAT(old_union_id, '->', new_union_id) SEPARATOR '; ') as changes
FROM unionid_migration_log
WHERE DATE(update_time) = CURDATE()
GROUP BY user_id
HAVING COUNT(*) > 2;

-- 3.5 数据一致性检查
-- 检查是否有用户的unionId未正确更新
SELECT
    u.userId,
    u.wxUnionId as current_union_id,
    ml.new_union_id as migrated_union_id,
    ml.update_time
FROM user u
LEFT JOIN unionid_migration_log ml ON u.userId = ml.user_id
WHERE u.wxUnionId IS NOT NULL
AND ml.new_union_id IS NOT NULL
AND u.wxUnionId != ml.new_union_id
LIMIT 10;
