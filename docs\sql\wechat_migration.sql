-- 微信应用迁移相关数据库脚本
-- 创建时间: 2025年1月11日
-- 用途: 支持微信开放平台应用迁移的unionId批量更新功能
-- 兼容: MySQL 5.7 GTID模式

-- 1. 创建 unionId 迁移记录表
CREATE TABLE unionid_migration_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL COMMENT '触发更新的用户ID',
    login_type TINYINT NOT NULL COMMENT '登录类型：0=QQ，1=微信',
    open_id VARCHAR(255) COMMENT 'openId',
    old_union_id VARCHAR(255) COMMENT '旧的unionId',
    new_union_id VARCHAR(255) COMMENT '新的unionId',
    update_type VARCHAR(20) DEFAULT 'batch_update' COMMENT '更新类型：batch_update=批量更新, manual_update=手动更新',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '更新时间',
    ip_address VARCHAR(45) COMMENT '更新时的IP地址',
    
    -- 索引优化
    INDEX idx_user_id (user_id),
    INDEX idx_update_time (update_time),
    INDEX idx_old_union_id (old_union_id),
    INDEX idx_new_union_id (new_union_id),
    INDEX idx_login_type (login_type),
    INDEX idx_update_type (update_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='UnionId迁移记录表';

-- 2. 创建数据备份表（兼容MySQL 5.7 GTID模式）
-- 先创建表结构
CREATE TABLE user_unionid_backup_20250111 (
    userId INT(11) NOT NULL,
    wxId VARCHAR(255) DEFAULT NULL,
    wxUnionId VARCHAR(255) DEFAULT NULL,
    qqId VARCHAR(255) DEFAULT NULL,
    qqUnionId VARCHAR(255) DEFAULT NULL,
    registerType TINYINT(4) DEFAULT NULL,
    userNickName VARCHAR(255) DEFAULT NULL,
    createTime DATETIME DEFAULT NULL,
    backup_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (userId),
    INDEX idx_wx_union_id (wxUnionId),
    INDEX idx_qq_union_id (qqUnionId),
    INDEX idx_backup_time (backup_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户unionId数据备份表';

-- 再插入备份数据
INSERT INTO user_unionid_backup_20250111 
(userId, wxId, wxUnionId, qqId, qqUnionId, registerType, userNickName, createTime, backup_time)
SELECT 
    userId,
    wxId,
    wxUnionId,
    qqId, 
    qqUnionId,
    registerType,
    userNickName,
    createTime,
    NOW()
FROM user 
WHERE wxUnionId IS NOT NULL OR qqUnionId IS NOT NULL;

-- 验证备份数据
SELECT 
    '备份表记录数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
UNION ALL
SELECT 
    '微信用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT 
    'QQ用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250111
WHERE qqUnionId IS NOT NULL;
