package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseMachine<M extends BaseMachine<M>> extends Model<M> implements IBean {

	public M setMachineId(java.lang.Integer machineId) {
		set("machineId", machineId);
		return (M)this;
	}
	
	public java.lang.Integer getMachineId() {
		return getInt("machineId");
	}

	public M setMachineName(java.lang.String machineName) {
		set("machineName", machineName);
		return (M)this;
	}
	
	public java.lang.String getMachineName() {
		return getStr("machineName");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
