package com.sandu.xinye.api.v2.user;

import com.jfinal.kit.HashKit;
import com.jfinal.kit.StrKit;
import com.jfinal.plugin.activerecord.Db;
import com.jfinal.plugin.activerecord.Page;
import com.jfinal.plugin.ehcache.CacheKit;
import com.sandu.xinye.api.model.XpUserPrefrence;
import com.sandu.xinye.common.constant.CacheConstant;
import com.sandu.xinye.common.kit.RetKit;
import com.sandu.xinye.common.model.*;
import com.xiaoleilu.hutool.json.JSONObject;
import com.xiaoleilu.hutool.util.CollectionUtil;
import com.xiaoleilu.hutool.util.StrUtil;

import java.util.Date;
import java.util.List;

import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_CLOSE;
import static com.sandu.xinye.common.constant.Constant.SAFE_MODE_OPEN;

public class UserService {

    public static final UserService me = new UserService();

    private static final String BeginTime = "2022-04-08";

    public RetKit list(int pageNumber, int pageSize) {
        Page<User> page = getAppleUsers(pageNumber, pageSize);
        return RetKit.ok("page", page);
    }

    public RetKit updateAppleUser(String oldAppleUserId, String newAppleUserId) {
        User model = User.dao.findFirst("select * from user where appleLoginUserId = ? ", oldAppleUserId);
        if (model == null) {
            return RetKit.fail("用户不存在！");
        }

        model.setAppleLoginUserId(newAppleUserId);

        boolean succ = model.update();
        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit updatePhone(String phone, String captcha, Integer userId) {
        User model = User.dao.findById(userId);
        if (model == null) {
            return RetKit.fail("用户不存在！");
        }

        if (checkAccountExists(phone)) {
            return RetKit.fail("手机号已注册，不能修改为该手机号！");
        }

        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码不正确！");
        }

        model.setUserPhone(phone).setUserNickName(phone);

        boolean succ = model.update();

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public RetKit bindPhone(String platform, String phone, String captcha, Integer userId) {
        User model = User.dao.findById(userId);
        if (model == null) {
            return RetKit.fail("用户不存在！");
        }

        String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone);
        if (!captcha.equals(realCaptcha)) {
            return RetKit.fail("验证码不正确！");
        }

        if (checkUserHasBindPhone(userId)) {
            return RetKit.fail("用户已绑定手机号，不支持继续绑定！");
        }

        // 获取被绑定手机号的账号
        User phoneAccount = getAccountByPhone(phone);
        if (phoneAccount == null) {
            // 手机号账号不存在，直接使用该账号作为手机号账号
            model.setUserImg("/upload/avatar/img.jpg").setUserNickName(phone).setUserPhone(phone);
            boolean succ = model.update();
            return succ ? RetKit.ok() : RetKit.fail();
        } else {
            // 用于用户绑定表
            String userBindId = "";

            // 手机号账号存在，把该账号删除，账号信息迁移到手机号
            // 原账号类型
            Integer registerType = model.getRegisterType();
            if (registerType == null) {
                registerType = User.REGISTER_TYPE_APPLE_USER;
            }
            if (registerType.equals(User.REGISTER_TYPE_QQ)) {
                userBindId = StrUtil.isNotEmpty(model.getQqUnionId()) ? model.getQqUnionId() : model.getQqId();

                // 一个手机号可以绑定多个QQ号（历史问题：android和ios采用不同的开发者账号）
                // QQ账号绑定手机号
                phoneAccount.setUserNickName(phone).setQqId(model.getQqId()).setOldQQId(model.getOldQQId())
                        .setQqUnionId(model.getQqUnionId())
                        .setQqNickName(model.getUserNickName());
            } else if (registerType.equals(User.REGISTER_TYPE_WX)) {
                // 微信应用迁移兼容：优先使用unionId，但要考虑迁移场景
                userBindId = StrUtil.isNotEmpty(model.getWxUnionId()) ? model.getWxUnionId() : model.getWxId();

                // 一个手机号只能绑定一个微信
                if (checkUserHasBindPhoneWithWx(phone)) {
                    return RetKit.fail("已有微信账号绑定该手机号，不能继续绑定！");
                }
                phoneAccount.setUserNickName(phone).setWxId(model.getWxId())
                        .setWxUnionId(model.getWxUnionId())
                        .setWxNickName(model.getUserNickName());
            } else if (registerType.equals(User.REGISTER_TYPE_APPLE_USER)) {
                userBindId = model.getAppleLoginUserId();

                if (checkUserHasBindPhoneWithApple(phone)) {
                    return RetKit.fail("已有苹果账号绑定该手机号，不能继续绑定！");
                }
                phoneAccount.setUserNickName(phone).setAppleLoginUserId(model.getAppleLoginUserId())
                        .setOldAppleLoginUserId(model.getOldAppleLoginUserId())
                        .setAppleNickName(model.getUserNickName());
            } else {
                return RetKit.fail("只有快捷登录账号可以绑定手机号！");
            }

            String finalUserBindId = userBindId;
            Integer finalRegisterType = registerType;

            // 微信应用迁移兼容：检查绑定时考虑unionId可能已更新的情况
            if (finalRegisterType.equals(User.REGISTER_TYPE_WX)) {
                // 对于微信，同时检查当前unionId和可能的旧unionId绑定记录
                if (checkUserHasBindWx(userId, finalUserBindId, model.getWxId())) {
                    return RetKit.fail("微信账号已绑定，不能重复绑定！");
                }
            } else if (checkUserHasBind(userId, finalUserBindId, finalRegisterType)) {
                // 其他类型账号的常规检查
                return RetKit.fail("账号已绑定，不能重复绑定！");
            }

            // 删除原账号
            boolean succ = Db.tx(() -> {
                // 插入一条数据到用户绑定表
                UserBind userBind = new UserBind().setUserId(phoneAccount.getUserId())
                        .setPlatform(platform);
                if (StrUtil.isNotEmpty(finalUserBindId)) {
                    userBind.setBindId(finalUserBindId).setBindType(finalRegisterType);
                    boolean succBind = userBind.save();
                    if (!succBind) {
                        return false;
                    }
                }

                // 迁移用户个人数据到新账号（手机账号）
                User user = User.dao.findById(userId);
                // 1.0 迁移模板分组和模板
                Db.update("update templet_group set userId = ? where userId = ?", phoneAccount.getUserId(), userId);
                Db.update("update templet set userId = ? where userId = ?", phoneAccount.getUserId(), userId);
                // 1.1 迁移调查问卷
                Db.update("update survey_answer set userId = ? where userId = ?", phoneAccount.getUserId(), userId);
                // 1.2 迁移我的文件
                Db.update("update cloudfile set userId = ? where userId = ?", phoneAccount.getUserId(), userId);

                // Remove everything related to specific userId in the database
                UserUnregister unregister = new UserUnregister().setUserId(user.getUserId())
                        .setUserPhone(user.getUserPhone()).setWxId(user.getWxId()).setQqId(user.getQqId()).setWxUnionId(user.getWxUnionId())
                        .setUserNickName(user.getUserNickName()).setRegisterType(user.getRegisterType()).setLastLoginTime(user.getLastLoginTime())
                        .setStatus(user.getStatus()).setUserImg(user.getUserImg())
                        .setUserPass(user.getUserPass()).setSalt(user.getSalt())
                        .setIdentity(user.getIdentity()).setCreateTime(user.getCreateTime())
                        .setUnregisterTime(new Date())
                        .setReason("绑定账号到手机号 " + phone + ",原账号注销！");
                boolean succ2 = unregister.save();
                if (!succ2) {
                    return false;
                }

                Db.delete("delete from user_login_log where userId = ?", userId);
                Db.delete("delete from feedback where userId = ?", userId);
                User.dao.deleteById(userId);

                // 原账号信息迁移到手机号
                boolean succ3 = phoneAccount.update();
                if (!succ3) {
                    return false;
                }
                return true;
            });

            if (succ) {
                // 清空该用户登录缓存
                removeCacheAndSession(userId);
            }

            // 获取token
            String userToken = getUserToken(phoneAccount.getUserId());
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("user", phoneAccount);
            jsonObject.put("token", userToken);

            return succ ? RetKit.ok().set("data", jsonObject) : RetKit.fail();
        }

    }

    public RetKit updatePassword(String oldPassword, String newPassword, Integer userId) {
        User model = User.dao.findById(userId);
        if (model == null) {
            return RetKit.fail("用户不存在！");
        }

        String salt = model.getSalt();
        String oldPwd = HashKit.sha256(salt + oldPassword);
        if (!model.getUserPass().equals(oldPwd)) {
            return RetKit.fail("旧密码不正确！");
        }

        // 修改成新密码
        salt = HashKit.generateSaltForSha256();
        String hashPwd = HashKit.sha256(salt + newPassword);
        boolean succ = model.setSalt(salt).setUserPass(hashPwd).update();
        if (succ) {
            removeCacheAndSession(model.getUserId());
        }

        return succ ? RetKit.ok() : RetKit.fail();
    }

    public Boolean checkAccountExists(String phoneOrEmail) {
        List<User> users = User.dao.find("select * from user where userPhone=?", phoneOrEmail);
        return CollectionUtil.isNotEmpty(users);
    }

    public Boolean checkUserHasBindPhone(Integer userId) {
        List<User> users = User.dao.find("select * from user where  userId=? and userPhone is not null", userId);
        return CollectionUtil.isNotEmpty(users);
    }

    public Boolean checkUserHasBindPhoneWithQQ(String phone) {
        List<User> users = User.dao.find("select * from user where  userPhone=? and qqId is not null", phone);
        return CollectionUtil.isNotEmpty(users);
    }

    public Boolean checkUserHasBindPhoneWithWx(String phone) {
        List<User> users = User.dao.find("select * from user where  userPhone=? and wxId is not null", phone);
        return CollectionUtil.isNotEmpty(users);
    }

    public Boolean checkUserHasBindPhoneWithApple(String phone) {
        List<User> users = User.dao.find("select * from user where  userPhone=? and appleLoginUserId is not null", phone);
        return CollectionUtil.isNotEmpty(users);
    }

    public Boolean checkUserHasBind(Integer userId, String bindId, Integer bindType) {
        List<UserBind> userBinds = UserBind.dao.find("select * from user_bind where userId=? and bindId=? and bindType=?", userId, bindId, bindType);
        return CollectionUtil.isNotEmpty(userBinds);
    }


    public Boolean checkPhoneHasBound(String phone) {
        List<User> users = User.dao.find("select * from user where userPhone=?", phone);
        return CollectionUtil.isNotEmpty(users);
    }

    public User getAccountByPhone(String phone) {
        User user = User.dao.findFirst("select * from user where userPhone=?", phone);
        return user;
    }

    private Page<User> getAppleUsers(int pageNumber, int pageSize) {
        return User.dao.paginate(pageNumber, pageSize, "select userId, appleLoginUserId, userNickName ",
                " from user where appleLoginUserId  is not null and lastLoginTime > ?", BeginTime);
    }

    private void removeCacheAndSession(Integer userId) {
        List<UserSession> list = UserSession.dao.find("select * from user_session where userId=?", userId);
        for (UserSession cus : list) {
            CacheKit.remove(CacheConstant.APP_USER, cus.getSessionId());
        }
        Db.update("delete from user_session where userId=?", userId);
    }

    private String getUserToken(Integer userId) {
        List<UserSession> list = UserSession.dao.find("select * from user_session where userId=? order by createTime desc", userId);
        if (list.size() > 0) {
            return list.get(0).getSessionId();
        }

        return null;
    }
      /**
     * 修改用户名和头像
     *
     * @return
     */
    public RetKit update(Integer userId, String userNickName, String userImg) {
      User user = User.dao.findById(userId);
      if (user == null) {
          return RetKit.fail("用户不存在!");
      }
      if (StrUtil.isEmpty(userNickName) && StrUtil.isEmpty(userImg)) {
          return RetKit.fail("用户名和头像不能同时为空！");
      }
      if (StrUtil.isNotEmpty(userNickName)) {
          user.setUserNickName(userNickName);
      }
      if(StrUtil.isNotEmpty(userImg)){
          user.setUserImg(userImg);
      }

      try {
          boolean succ = user.update();
          if (succ) {
              return RetKit.ok("data", user.getUserId());
          }
      } catch (Exception ex) {
          return RetKit.fail(ex.getMessage());
      }

      return RetKit.fail("修改用户信息失败！");
  }

    /**
     * 获取个人的喜好配置
     */
    public RetKit preference(User user) {
        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", user.getUserId());
        return RetKit.ok("preference", preference);
    }

    public RetKit updatePreference(User user, XpUserPrefrence xpUserPrefrence) {
        RetKit retKit;
        int mode = xpUserPrefrence.getSafeMode();
        if (mode == -1 || (mode != SAFE_MODE_OPEN && mode != SAFE_MODE_CLOSE)) {
            retKit = RetKit.fail("安全模式只能传入0或者1！");
            return retKit;
        }
        String captcha = xpUserPrefrence.getCaptcha();
        String phone = xpUserPrefrence.getPhone();
        if (mode == 0) {
            if (StrKit.isBlank(captcha) || StrKit.isBlank(phone)) {
                return RetKit.fail("手机号和验证码不能为空！");
            }
            String realCaptcha = CacheKit.get(CacheConstant.CAPTCHA, phone.trim());
            if (realCaptcha == null) {
                return RetKit.fail("验证码已过期，请重新发送验证码！");
            }
            if (!captcha.trim().equals(realCaptcha)) {
                return RetKit.fail("验证码错误！");
            }
        }

        UserPreference preference = UserPreference.dao.findFirst("select * from user_preference where user_id = ? ", user.getUserId());
        if (preference == null) {
            preference = new UserPreference();
            preference.setUserId(user.getUserId());
            preference.setSafeMode(mode);
            boolean succ = preference.save();
            return succ ? RetKit.ok() : RetKit.fail();
        } else {
            preference.setSafeMode(mode);
            boolean succ = preference.update();
            return succ ? RetKit.ok() : RetKit.fail();
        }

    }

    /**
     * 检查微信用户是否已绑定（兼容应用迁移场景）
     * 同时检查unionId和openId，避免因unionId变化导致的绑定检查失效
     */
    public Boolean checkUserHasBindWx(Integer userId, String unionId, String openId) {
        // 参数验证
        if (userId == null || StrUtil.isEmpty(unionId) || StrUtil.isEmpty(openId)) {
            return false;
        }

        // 性能优化：一次查询同时检查unionId和openId
        String sql = "SELECT * FROM user_bind WHERE userId=? AND bindType=? AND (bindId=? OR bindId=?)";
        List<UserBind> binds = UserBind.dao.find(sql, userId, User.REGISTER_TYPE_WX, unionId, openId);

        return CollectionUtil.isNotEmpty(binds);
    }

}
