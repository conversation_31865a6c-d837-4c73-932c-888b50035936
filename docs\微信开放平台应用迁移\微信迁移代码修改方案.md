# 微信应用迁移代码修改方案

## 🎯 修改目标
采用批量更新策略，当检测到 unionId 变化时，同步更新所有相同旧 unionId 的用户账号。基于**微信 unionId 相同代表同一个用户**的原则，确保跨端数据同步功能正常工作。

## 💡 方案优势
- ✅ **无过渡期**：一次登录，所有关联账号立即同步
- ✅ **用户无感知**：跨端数据立即可用，体验最佳  
- ✅ **查询性能不变**：不需要修改任何查询逻辑
- ✅ **实现简单**：代码改动最小，业务逻辑正确

## 📍 需要修改的文件

### 1. UserLoginService.java
**文件位置**: `src/main/java/com/sandu/xinye/api/v2/login/UserLoginService.java`

#### 修改1: 批量更新 unionId 逻辑
**位置**: 第561-574行

**原代码**:
```java
if (loginType == User.REGISTER_TYPE_WX
        && StrUtil.isEmpty(checkUser.getWxUnionId())
        && StrUtil.isNotEmpty(unionId)) {
    checkUser.setWxUnionId(unionId);
    try {
        boolean succ = checkUser.save();
        if (!succ) {
            LogKit.info("微信登录失败，用户openId" + checkUser.getWxId());
        }
    } catch (Exception e) {
        LogKit.info("微信登录失败，用户昵称" + checkUser.getUserNickName());
        LogKit.error("微信登录失败：" + e.getMessage());
    }
}
```

**修改后（批量更新方案）**:
```java
if (loginType == User.REGISTER_TYPE_WX
        && StrUtil.isNotEmpty(unionId)) {
    
    String currentUnionId = checkUser.getWxUnionId();
    boolean needUpdate = false;
    
    if (StrUtil.isEmpty(currentUnionId)) {
        // 情况1：首次保存 unionId
        needUpdate = true;
        LogKit.info("首次保存unionId - userId: " + checkUser.getUserId() + ", unionId: " + unionId);
    } else if (!unionId.equals(currentUnionId)) {
        // 情况2：unionId 发生变化（应用迁移场景）
        needUpdate = true;
        LogKit.info("检测到UnionId变化，开始批量更新 - userId: " + checkUser.getUserId() + 
                   ", old: " + currentUnionId + ", new: " + unionId);
    }
    
    if (needUpdate) {
        // 批量更新所有相同 unionId 的用户账号
        boolean success = Db.tx(() -> {
            try {
                if (StrUtil.isNotEmpty(currentUnionId)) {
                    // 批量更新所有相同旧 unionId 的用户
                    int updatedCount = Db.update(
                        "UPDATE user SET wxUnionId = ? WHERE wxUnionId = ?",
                        unionId, currentUnionId
                    );
                    
                    LogKit.info("UnionId批量更新完成 - 旧unionId: " + currentUnionId + 
                               ", 新unionId: " + unionId + ", 影响用户数: " + updatedCount);
                    
                    // 记录批量更新日志
                    Db.update(
                        "INSERT INTO unionid_migration_log " +
                        "(user_id, login_type, old_union_id, new_union_id, update_type, ip_address) " +
                        "VALUES (?, ?, ?, ?, 'batch_update', ?)",
                        checkUser.getUserId(), User.REGISTER_TYPE_WX, currentUnionId, unionId, ip
                    );
                } else {
                    // 首次保存的情况，只更新当前用户
                    checkUser.setWxUnionId(unionId);
                    checkUser.update();
                    
                    LogKit.info("首次保存unionId成功 - userId: " + checkUser.getUserId());
                }
                
                return true;
            } catch (Exception e) {
                LogKit.error("UnionId批量更新失败: " + e.getMessage(), e);
                return false;
            }
        });
        
        // 更新当前用户对象的 unionId（因为对象可能还要继续使用）
        if (success) {
            checkUser.setWxUnionId(unionId);
        } else {
            LogKit.error("UnionId批量更新事务失败，但不影响用户登录");
        }
    }
}
```

#### 修改2: QQ unionId 也采用批量更新
**位置**: 第574-587行

**原代码**:
```java
else if (loginType == User.REGISTER_TYPE_QQ
        && StrUtil.isEmpty(checkUser.getQqUnionId())
        && StrUtil.isNotEmpty(unionId)) {
    checkUser.setQqUnionId(unionId);
    // ... 保存逻辑
}
```

**修改后（与微信保持一致的批量更新策略）**:
```java
else if (loginType == User.REGISTER_TYPE_QQ
        && StrUtil.isNotEmpty(unionId)) {
    
    String currentQqUnionId = checkUser.getQqUnionId();
    boolean needUpdate = false;
    
    if (StrUtil.isEmpty(currentQqUnionId)) {
        needUpdate = true;
        LogKit.info("首次保存QQ unionId - userId: " + checkUser.getUserId() + ", unionId: " + unionId);
    } else if (!unionId.equals(currentQqUnionId)) {
        needUpdate = true;
        LogKit.info("检测到QQ UnionId变化，开始批量更新 - userId: " + checkUser.getUserId() + 
                   ", old: " + currentQqUnionId + ", new: " + unionId);
    }
    
    if (needUpdate) {
        // QQ 也采用批量更新策略
        boolean success = Db.tx(() -> {
            try {
                if (StrUtil.isNotEmpty(currentQqUnionId)) {
                    int updatedCount = Db.update(
                        "UPDATE user SET qqUnionId = ? WHERE qqUnionId = ?",
                        unionId, currentQqUnionId
                    );
                    
                    LogKit.info("QQ UnionId批量更新完成 - 旧unionId: " + currentQqUnionId + 
                               ", 新unionId: " + unionId + ", 影响用户数: " + updatedCount);
                    
                    // 记录QQ批量更新日志
                    Db.update(
                        "INSERT INTO unionid_migration_log " +
                        "(user_id, login_type, old_union_id, new_union_id, update_type, ip_address) " +
                        "VALUES (?, ?, ?, ?, 'batch_update', ?)",
                        checkUser.getUserId(), User.REGISTER_TYPE_QQ, currentQqUnionId, unionId, ip
                    );
                } else {
                    checkUser.setQqUnionId(unionId);
                    checkUser.update();
                    LogKit.info("首次保存QQ unionId成功 - userId: " + checkUser.getUserId());
                }
                
                return true;
            } catch (Exception e) {
                LogKit.error("QQ UnionId批量更新失败: " + e.getMessage(), e);
                return false;
            }
        });
        
        if (success) {
            checkUser.setQqUnionId(unionId);
        } else {
            LogKit.error("QQ UnionId批量更新事务失败，但不影响用户登录");
        }
    }
}
```

## 📊 数据库准备

### 1. 创建迁移监控表
```sql
-- 创建 unionId 迁移记录表
CREATE TABLE unionid_migration_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_type TINYINT NOT NULL COMMENT '登录类型：0=QQ，1=微信',
    open_id VARCHAR(255) COMMENT 'openId',
    old_union_id VARCHAR(255) COMMENT '旧的unionId',
    new_union_id VARCHAR(255) COMMENT '新的unionId',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) COMMENT '更新时的IP地址',
    INDEX idx_user_id (user_id),
    INDEX idx_update_time (update_time),
    INDEX idx_old_union_id (old_union_id),
    INDEX idx_new_union_id (new_union_id)
) COMMENT='UnionId迁移记录表';
```

### 2. 数据备份脚本
```sql
-- 执行前备份当前 unionId 数据
CREATE TABLE user_unionid_backup_20250109 AS
SELECT 
    userId,
    wxId,
    wxUnionId,
    qqId, 
    qqUnionId,
    registerType,
    createTime,
    NOW() as backup_time
FROM user 
WHERE wxUnionId IS NOT NULL OR qqUnionId IS NOT NULL;
```

## 🔍 增强的日志记录方案

### 批量更新日志记录
日志记录已经集成在批量更新的事务中，确保数据一致性：

```java
// 在批量更新事务中记录日志
Db.update(
    "INSERT INTO unionid_migration_log " +
    "(user_id, login_type, old_union_id, new_union_id, update_type, ip_address) " +
    "VALUES (?, ?, ?, ?, 'batch_update', ?)",
    checkUser.getUserId(), loginType, oldUnionId, unionId, ip
);
```

**日志记录特点**：
- ✅ **事务一致性**: 日志记录和数据更新在同一事务中
- ✅ **触发用户记录**: 记录触发批量更新的用户ID
- ✅ **完整变更追踪**: 记录新旧unionId，便于审计和回滚
- ✅ **批量标识**: update_type标识为'batch_update'

### Controller层无需修改
**UserLoginService.java** fasterLogin 方法签名已经包含 ip 参数，可以直接使用。

## 🧪 测试方案

### 1. 单元测试用例
创建测试用例验证 unionId 更新逻辑：

```java
@Test
public void testUnionIdUpdate() {
    // 1. 创建测试用户，设置旧的 unionId
    // 2. 模拟登录，传入新的 unionId  
    // 3. 验证 unionId 是否正确更新
    // 4. 验证日志是否正确记录
}
```

### 2. 集成测试
- 模拟迁移前后的登录流程
- 验证跨端数据查询功能

## 📈 监控和统计

### 1. 批量更新监控
```sql
-- 查看批量更新统计
SELECT 
    DATE(update_time) as date,
    login_type,
    old_union_id,
    new_union_id,
    COUNT(DISTINCT user_id) as trigger_users,
    update_type
FROM unionid_migration_log 
WHERE update_type = 'batch_update'
GROUP BY DATE(update_time), login_type, old_union_id, new_union_id
ORDER BY date DESC;
```

### 2. 迁移完成度监控
```sql
-- 统计迁移进度
SELECT 
    '迁移前用户数' as metric,
    COUNT(*) as value
FROM user_unionid_backup_20250109
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT 
    '已完成迁移的unionId数量' as metric,
    COUNT(DISTINCT old_union_id) as value
FROM unionid_migration_log 
WHERE login_type = 1 AND update_type = 'batch_update';

-- 查看具体的批量更新情况
SELECT 
    old_union_id,
    new_union_id,
    MIN(update_time) as first_update,
    COUNT(DISTINCT user_id) as affected_users
FROM unionid_migration_log 
WHERE update_type = 'batch_update' AND login_type = 1
GROUP BY old_union_id, new_union_id
ORDER BY first_update DESC;
```

## ✅ 部署计划

### 1. 预发布环境验证
- 部署修改后的代码
- 执行数据库表创建
- 进行功能测试

### 2. 生产环境部署
- 在业务低峰期部署
- 创建监控表和备份数据
- 发布代码更新
- 密切关注日志和监控指标

### 3. 后续验证
- 监控 unionId 更新日志
- 验证跨端功能正常性
- 统计迁移完成情况

## 📋 部署检查清单

- [ ] 创建数据库备份表
- [ ] 创建 unionId 迁移监控表  
- [ ] 部署代码更新
- [ ] 验证测试环境功能
- [ ] 准备监控和告警
- [ ] 制定回滚方案

## 🚨 风险控制

1. **代码回滚方案**: 保留原始代码版本，必要时快速回滚
2. **数据恢复方案**: 通过备份表可以恢复 unionId 数据
3. **灰度发布**: 可考虑先在部分用户上验证
4. **监控告警**: 设置 unionId 更新异常的告警机制

## 📝 总结

此修改方案的核心优势：
1. **无停服迁移**: 用户登录时自动更新 unionId
2. **完整日志**: 记录所有 unionId 变化，便于排查问题
3. **向后兼容**: 不影响现有功能，只是增强了更新机制  
4. **可监控**: 提供完整的迁移进度和异常监控能力