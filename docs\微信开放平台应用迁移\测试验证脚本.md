# 微信应用迁移测试验证脚本

## 📋 测试概述
本文档提供了微信应用迁移功能的测试验证方法，确保批量更新unionId功能正常工作。

## 🧪 测试环境准备

### 1. 数据库准备
```sql
-- 1. 创建测试用的监控表（如果还未创建）
CREATE TABLE IF NOT EXISTS unionid_migration_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_type TINYINT NOT NULL COMMENT '登录类型：0=QQ，1=微信',
    old_union_id VARCHAR(255) COMMENT '旧的unionId',
    new_union_id VARCHAR(255) COMMENT '新的unionId',
    update_type VARCHAR(20) DEFAULT 'batch_update',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45),
    INDEX idx_user_id (user_id),
    INDEX idx_update_time (update_time)
);

-- 2. 创建测试数据
INSERT INTO user (userNickName, wxId, wxUnionId, registerType, createTime, status) VALUES
('测试用户1', 'test_openid_1', 'old_union_id_123', 1, NOW(), 2),
('测试用户2', 'test_openid_2', 'old_union_id_123', 1, NOW(), 2),
('测试用户3', 'test_openid_3', 'old_union_id_456', 1, NOW(), 2);

-- 3. 验证测试数据
SELECT userId, userNickName, wxId, wxUnionId FROM user WHERE userNickName LIKE '测试用户%';
```

## 🔍 功能测试用例

### 测试用例1: 首次保存unionId
**测试场景**: 新用户首次登录，保存unionId
**测试数据**: 
- openId: new_user_openid
- unionId: new_union_id_789
- 预期: 正常保存，不触发批量更新

**验证方法**:
```sql
-- 检查用户数据
SELECT userId, wxId, wxUnionId FROM user WHERE wxId = 'new_user_openid';

-- 检查是否有迁移日志（应该没有）
SELECT * FROM unionid_migration_log WHERE new_union_id = 'new_union_id_789';
```

### 测试用例2: unionId变化触发批量更新
**测试场景**: 现有用户登录时unionId发生变化
**测试数据**:
- openId: test_openid_1 (已存在)
- 旧unionId: old_union_id_123
- 新unionId: new_union_id_123
- 预期: 触发批量更新，所有相同旧unionId的用户都更新

**验证方法**:
```sql
-- 1. 检查批量更新结果
SELECT userId, userNickName, wxId, wxUnionId 
FROM user 
WHERE wxUnionId = 'new_union_id_123';

-- 2. 检查迁移日志
SELECT * FROM unionid_migration_log 
WHERE old_union_id = 'old_union_id_123' 
AND new_union_id = 'new_union_id_123';

-- 3. 验证影响的用户数量
SELECT COUNT(*) as affected_users 
FROM user 
WHERE wxUnionId = 'new_union_id_123';
```

### 测试用例3: QQ unionId批量更新
**测试场景**: QQ用户unionId变化
**测试数据**:
```sql
-- 创建QQ测试数据
INSERT INTO user (userNickName, qqId, qqUnionId, registerType, createTime, status) VALUES
('QQ测试用户1', 'qq_openid_1', 'old_qq_union_123', 0, NOW(), 2),
('QQ测试用户2', 'qq_openid_2', 'old_qq_union_123', 0, NOW(), 2);
```

**验证方法**:
```sql
-- 检查QQ批量更新结果
SELECT userId, userNickName, qqId, qqUnionId 
FROM user 
WHERE qqUnionId = 'new_qq_union_123';

-- 检查QQ迁移日志
SELECT * FROM unionid_migration_log 
WHERE login_type = 0 
AND old_union_id = 'old_qq_union_123';
```

## 📊 性能测试

### 1. 批量更新性能测试
```sql
-- 创建大量测试数据
INSERT INTO user (userNickName, wxId, wxUnionId, registerType, createTime, status)
SELECT 
    CONCAT('性能测试用户', @row_number := @row_number + 1),
    CONCAT('perf_test_openid_', @row_number),
    'perf_test_union_id',
    1,
    NOW(),
    2
FROM 
    (SELECT @row_number := 0) r,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t1,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t2,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t3,
    (SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5) t4
LIMIT 100;

-- 测试批量更新性能
SET @start_time = NOW(6);
UPDATE user SET wxUnionId = 'new_perf_test_union_id' WHERE wxUnionId = 'perf_test_union_id';
SET @end_time = NOW(6);
SELECT TIMESTAMPDIFF(MICROSECOND, @start_time, @end_time) / 1000 as execution_time_ms;
```

## 🚨 异常测试

### 1. 事务回滚测试
**测试场景**: 模拟批量更新过程中的异常
```sql
-- 临时修改表结构制造异常（仅测试环境）
-- ALTER TABLE unionid_migration_log DROP COLUMN user_id;

-- 执行登录操作，观察是否正确回滚
-- 恢复表结构
-- ALTER TABLE unionid_migration_log ADD COLUMN user_id BIGINT NOT NULL;
```

### 2. 并发测试
**测试场景**: 多个用户同时登录触发批量更新
- 使用多个线程同时调用登录接口
- 验证数据一致性
- 检查是否有重复的迁移日志

## 📋 验证检查清单

### 功能验证
- [ ] 首次保存unionId功能正常
- [ ] unionId变化时触发批量更新
- [ ] 批量更新影响正确数量的用户
- [ ] 迁移日志记录完整准确
- [ ] QQ unionId批量更新功能正常
- [ ] 事务回滚机制正常工作

### 性能验证
- [ ] 批量更新性能在可接受范围内
- [ ] 不影响正常登录流程性能
- [ ] 数据库连接池正常工作

### 数据验证
- [ ] 用户数据完整性保持
- [ ] 跨端数据同步功能正常
- [ ] 无数据丢失或重复

### 日志验证
- [ ] 关键操作日志记录完整
- [ ] 异常情况日志记录准确
- [ ] 日志格式便于监控和分析

## 🔧 测试工具脚本

### 1. 快速验证脚本
```sql
-- 一键验证当前迁移状态
SELECT 
    '总用户数' as metric,
    COUNT(*) as value
FROM user
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT 
    '迁移记录数' as metric,
    COUNT(*) as value
FROM unionid_migration_log
UNION ALL
SELECT 
    '今日迁移数' as metric,
    COUNT(*) as value
FROM unionid_migration_log
WHERE DATE(update_time) = CURDATE();
```

### 2. 数据清理脚本
```sql
-- 清理测试数据（仅测试环境使用）
DELETE FROM user WHERE userNickName LIKE '测试用户%' OR userNickName LIKE 'QQ测试用户%' OR userNickName LIKE '性能测试用户%';
DELETE FROM unionid_migration_log WHERE old_union_id LIKE '%test%' OR new_union_id LIKE '%test%';
```

## 📝 测试报告模板

```
微信应用迁移功能测试报告

测试时间: YYYY-MM-DD
测试环境: [测试/预发布]
测试负责人: XXX

1. 功能测试结果
   - 首次保存unionId: [通过/失败]
   - 批量更新功能: [通过/失败]
   - 事务回滚机制: [通过/失败]
   - 日志记录功能: [通过/失败]

2. 性能测试结果
   - 批量更新100用户耗时: XXXms
   - 登录接口响应时间: XXXms
   - 数据库连接池状态: 正常

3. 发现的问题
   - 问题1: 描述
   - 问题2: 描述

4. 测试结论
   - 整体评估: [通过/需要修复]
   - 建议: XXX
```

---
*测试脚本版本: v1.0*  
*创建时间: 2025年1月11日*
