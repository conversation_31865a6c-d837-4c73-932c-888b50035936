package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseAppVersion<M extends BaseAppVersion<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setAndroidVersion(java.lang.String androidVersion) {
		set("androidVersion", androidVersion);
		return (M)this;
	}
	
	public java.lang.String getAndroidVersion() {
		return getStr("androidVersion");
	}

	public M setAndroidApk(java.lang.String androidApk) {
		set("androidApk", androidApk);
		return (M)this;
	}
	
	public java.lang.String getAndroidApk() {
		return getStr("androidApk");
	}

	public M setIosVersion(java.lang.String iosVersion) {
		set("iosVersion", iosVersion);
		return (M)this;
	}
	
	public java.lang.String getIosVersion() {
		return getStr("iosVersion");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

	public M setAndroidInfo(java.lang.String androidInfo) {
		set("androidInfo", androidInfo);
		return (M)this;
	}
	
	public java.lang.String getAndroidInfo() {
		return getStr("androidInfo");
	}

}
