package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JF<PERSON>, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseAbout<M extends BaseAbout<M>> extends Model<M> implements IBean {

	public M setAboutId(java.lang.Integer aboutId) {
		set("aboutId", aboutId);
		return (M)this;
	}
	
	public java.lang.Integer getAboutId() {
		return getInt("aboutId");
	}

	public M setAboutImg(java.lang.String aboutImg) {
		set("aboutImg", aboutImg);
		return (M)this;
	}
	
	public java.lang.String getAboutImg() {
		return getStr("aboutImg");
	}

	public M setSysUserId(java.lang.Integer sysUserId) {
		set("sysUserId", sysUserId);
		return (M)this;
	}
	
	public java.lang.Integer getSysUserId() {
		return getInt("sysUserId");
	}

	public M setCreateTime(java.util.Date createTime) {
		set("createTime", createTime);
		return (M)this;
	}
	
	public java.util.Date getCreateTime() {
		return get("createTime");
	}

}
