# 微信应用迁移操作手册

## 📋 迁移概述
- **迁移类型**: App端和Web端同时迁移到新的微信开放平台账号
- **关键变化**: unionId 会变更，openId 保持不变
- **迁移方式**: 无停服平滑迁移，采用批量更新策略
- **核心原则**: 微信 unionId 相同代表同一个用户，批量同步更新所有关联账号
- **预计影响**: 用户完全无感知，跨端数据立即恢复，无过渡期

## 🕐 时间安排

### 阶段1: 准备阶段（迁移审核前）
**预计时长**: 2-3个工作日

### 阶段2: 执行阶段（审核通过后）
**预计时长**: 即时完成（批量更新策略，用户登录时立即完成所有关联账号更新）

### 阶段3: 验证阶段（迁移后）
**预计时长**: 1-2周（持续监控批量更新效果）

## 📝 详细操作步骤

### 🔧 阶段1: 迁移前准备

#### 步骤1.1: 数据备份
**执行时间**: 迁移前1天
**责任人**: DBA/后端开发

```sql
-- 1. 备份当前所有 unionId 数据
CREATE TABLE user_unionid_backup_20250109 AS
SELECT 
    userId,
    wxId,
    wxUnionId,
    qqId, 
    qqUnionId,
    registerType,
    userNickName,
    createTime,
    NOW() as backup_time
FROM user 
WHERE wxUnionId IS NOT NULL OR qqUnionId IS NOT NULL;

-- 验证备份数据
SELECT 
    registerType,
    COUNT(*) as user_count,
    COUNT(DISTINCT wxUnionId) as unique_wx_unions,
    COUNT(DISTINCT qqUnionId) as unique_qq_unions
FROM user_unionid_backup_20250109 
GROUP BY registerType;
```

#### 步骤1.2: 创建迁移监控表
**执行时间**: 迁移前1天

```sql
-- 创建 unionId 迁移记录表
CREATE TABLE unionid_migration_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_type TINYINT NOT NULL COMMENT '登录类型：0=QQ，1=微信',
    open_id VARCHAR(255) COMMENT 'openId',
    old_union_id VARCHAR(255) COMMENT '旧的unionId',
    new_union_id VARCHAR(255) COMMENT '新的unionId',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    ip_address VARCHAR(45) COMMENT '更新时的IP地址',
    INDEX idx_user_id (user_id),
    INDEX idx_update_time (update_time),
    INDEX idx_old_union_id (old_union_id),
    INDEX idx_new_union_id (new_union_id)
) COMMENT='UnionId迁移记录表';
```

#### 步骤1.3: 部署代码更新
**执行时间**: 迁移前1天
**责任人**: 后端开发

1. **更新 UserLoginService.java**
   - 实现批量更新 unionId 逻辑
   - 集成事务和日志记录
   - 支持微信和QQ的批量更新策略
   
2. **测试验证**
   - 在测试环境验证批量更新功能
   - 确保不影响现有登录流程
   - 验证跨端数据同步效果

3. **生产部署**
   - 选择业务低峰期部署
   - 密切监控服务状态和批量更新日志

#### 步骤1.4: 监控准备
**执行时间**: 迁移前1天

1. **配置日志监控**
   - 关键词: "UnionId迁移更新"
   - 异常关键词: "unionId更新失败"

2. **准备统计查询**
   ```sql
   -- 批量更新监控查询
   SELECT 
       DATE(update_time) as date,
       old_union_id,
       new_union_id,
       COUNT(DISTINCT user_id) as trigger_users,
       update_type
   FROM unionid_migration_log 
   WHERE update_type = 'batch_update'
   GROUP BY DATE(update_time), old_union_id, new_union_id
   ORDER BY date DESC;
   ```

### 🚀 阶段2: 迁移执行

#### 步骤2.1: 微信审核通过确认
**责任人**: 产品/运营

1. **确认迁移生效**
   - 测试新的 unionId 获取
   - 确认 App 和 Web 同时生效

2. **通知团队**
   - 技术团队开始密切监控
   - 客服团队准备应对用户咨询

#### 步骤2.2: 实时监控
**持续时间**: 迁移后7天
**检查频率**: 前24小时每2小时检查一次，后续每天检查

**监控项目**:
1. **服务状态监控**
   - 登录成功率
   - 接口响应时间
   - 错误日志数量

2. **批量更新监控**
   ```sql
   -- 每日批量更新统计
   SELECT 
       DATE(update_time) as date,
       COUNT(DISTINCT old_union_id) as updated_union_groups,
       COUNT(DISTINCT user_id) as trigger_users
   FROM unionid_migration_log 
   WHERE DATE(update_time) = CURDATE() AND update_type = 'batch_update'
   GROUP BY DATE(update_time);
   
   -- 检查批量更新的影响范围
   SELECT 
       old_union_id,
       new_union_id,
       COUNT(DISTINCT user_id) as affected_users
   FROM unionid_migration_log
   WHERE DATE(update_time) = CURDATE() AND update_type = 'batch_update'
   GROUP BY old_union_id, new_union_id
   ORDER BY affected_users DESC;
   ```

3. **异常检测**
   ```sql
   -- 检查异常情况
   -- 1. 同一用户多次更新unionId
   SELECT user_id, COUNT(*) as update_count
   FROM unionid_migration_log 
   WHERE DATE(update_time) = CURDATE()
   GROUP BY user_id 
   HAVING COUNT(*) > 2;
   
   -- 2. 检查失败的登录尝试
   SELECT COUNT(*) as failed_logins
   FROM log_table 
   WHERE message LIKE '%unionId更新失败%' 
   AND DATE(create_time) = CURDATE();
   ```

### ✅ 阶段3: 迁移后验证

#### 步骤3.1: 功能验证测试
**执行时间**: 迁移后第1天

**测试用例**:
1. **用户登录测试**
   ```
   测试场景: 老用户通过微信登录App
   预期结果: 登录成功，unionId自动更新
   验证方法: 检查迁移日志表有对应记录
   ```

2. **跨端数据同步测试**
   ```
   测试场景: 用户在App端创建模板，Web端查看
   预期结果: 能正常看到App端创建的模板
   验证方法: 直接功能测试
   ```

3. **手机号绑定测试**
   ```
   测试场景: 用户绑定/解绑手机号
   预期结果: 功能正常，无报错
   验证方法: 功能测试
   ```

#### 步骤3.2: 数据完整性检查
**执行时间**: 迁移后第3天、第7天

```sql
-- 1. 检查迁移完成率
SELECT 
    '总用户数' as metric,
    COUNT(*) as value
FROM user 
WHERE wxUnionId IS NOT NULL
UNION ALL
SELECT 
    '已迁移用户数' as metric,
    COUNT(DISTINCT user_id) as value
FROM unionid_migration_log;

-- 2. 检查数据一致性
SELECT 
    u.userId,
    u.wxUnionId as current_union_id,
    ml.new_union_id as migrated_union_id
FROM user u
LEFT JOIN unionid_migration_log ml ON u.userId = ml.user_id
WHERE u.wxUnionId IS NOT NULL 
AND u.wxUnionId != ml.new_union_id
LIMIT 10;
```

#### 步骤3.3: 用户反馈收集
**执行时间**: 迁移后1周内

1. **主动监控**
   - 客服反馈的登录问题
   - 用户反馈的功能异常

2. **数据分析**
   - 登录成功率对比
   - 跨端功能使用率变化

## 🚨 异常处理预案

### 异常1: 用户登录失败
**症状**: 用户无法通过微信登录
**可能原因**: openId 查询失败
**处理方案**:
```sql
-- 检查用户数据
SELECT userId, wxId, wxUnionId, registerType 
FROM user 
WHERE wxId = '用户的openId';

-- 如果找不到，检查是否有相同unionId的记录
SELECT userId, wxId, wxUnionId, registerType 
FROM user 
WHERE wxUnionId = '用户的unionId';
```

### 异常2: 跨端数据不同步
**症状**: 用户在Web端看不到App端的数据
**可能原因**: unionId 未及时更新
**处理方案**:
```sql
-- 手动更新unionId
UPDATE user 
SET wxUnionId = '新的unionId' 
WHERE userId = '用户ID';

-- 记录手动更新日志
INSERT INTO unionid_migration_log 
(user_id, login_type, old_union_id, new_union_id, update_time, ip_address)
VALUES 
('用户ID', 1, '旧unionId', '新unionId', NOW(), 'manual_update');
```

### 异常3: 大量用户反馈问题
**应急方案**: 
1. 立即回滚到原代码版本
2. 通过备份表恢复 unionId 数据
3. 通知微信平台，申请暂缓迁移

```sql
-- 紧急回滚数据（仅在必要时使用）
UPDATE user u
INNER JOIN user_unionid_backup_20250109 b ON u.userId = b.userId
SET u.wxUnionId = b.wxUnionId
WHERE u.wxUnionId != b.wxUnionId;
```

## 📊 监控报告模板

### 日报模板
```
微信应用迁移日报 - 2025年X月X日

1. 迁移进度
   - 今日新增迁移用户: XX人
   - 累计完成迁移: XX人
   - 预计完成率: XX%

2. 服务状态
   - 登录成功率: XX%
   - 接口平均响应时间: XXms
   - 异常日志数量: XX条

3. 用户反馈
   - 用户投诉: XX起
   - 主要问题: XXX
   - 处理状态: XXX

4. 风险评估
   - 风险等级: 低/中/高
   - 主要风险: XXX
   - 应对措施: XXX
```

### 完成报告模板
```
微信应用迁移完成报告

1. 迁移概况
   - 迁移开始时间: YYYY-MM-DD
   - 迁移完成时间: YYYY-MM-DD
   - 总影响用户数: XXX人
   - 迁移成功率: XX%

2. 技术指标
   - 服务可用性: XX%
   - 数据完整性: XX%
   - 功能正确性: XX%

3. 用户影响
   - 用户投诉数量: XX起
   - 问题解决率: XX%
   - 用户满意度: XX

4. 经验总结
   - 成功经验: XXX
   - 改进建议: XXX
   - 风险防范: XXX
```

## ✅ 验收标准

### 技术验收
- [ ] 所有活跃用户的 unionId 完成更新
- [ ] 跨端数据同步功能正常
- [ ] 用户登录成功率 > 99.9%
- [ ] 无数据丢失或错误

### 业务验收
- [ ] 用户投诉数量 < 正常水平的110%
- [ ] 核心功能使用率无明显下降
- [ ] 客服满意度评分正常

### 运维验收
- [ ] 系统监控指标正常
- [ ] 日志记录完整
- [ ] 应急预案可执行
- [ ] 文档归档完整

## 📚 相关文档
- [微信应用迁移影响分析.md](./微信应用迁移影响分析.md)
- [微信迁移代码修改方案.md](./微信迁移代码修改方案.md)
- 微信开放平台迁移官方文档

## 👥 责任分工
- **项目负责人**: 整体协调和决策
- **后端开发**: 代码修改和部署
- **DBA**: 数据库操作和监控
- **运维**: 系统监控和应急响应
- **测试**: 功能验证和测试
- **客服**: 用户反馈收集和处理

---
*文档版本: v1.0*  
*创建时间: 2025年1月9日*  
*最后更新: 2025年1月9日*