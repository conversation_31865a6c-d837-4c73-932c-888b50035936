package com.sandu.xinye.common.model.base;

import com.jfinal.plugin.activerecord.Model;
import com.jfinal.plugin.activerecord.IBean;

/**
 * Generated by JFinal, do not modify this file.
 */
@SuppressWarnings({"serial", "unchecked"})
public abstract class BaseHelpI18n<M extends BaseHelpI18n<M>> extends Model<M> implements IBean {

	public M setId(java.lang.Integer id) {
		set("id", id);
		return (M)this;
	}
	
	public java.lang.Integer getId() {
		return getInt("id");
	}

	public M setLocale(java.lang.String locale) {
		set("locale", locale);
		return (M)this;
	}
	
	public java.lang.String getLocale() {
		return getStr("locale");
	}

	public M setHelpId(java.lang.Integer helpId) {
		set("helpId", helpId);
		return (M)this;
	}
	
	public java.lang.Integer getHelpId() {
		return getInt("helpId");
	}

	public M setHelpVideo(java.lang.String helpVideo) {
		set("helpVideo", helpVideo);
		return (M)this;
	}
	
	public java.lang.String getHelpVideo() {
		return getStr("helpVideo");
	}

	public M setHelpName(java.lang.String helpName) {
		set("helpName", helpName);
		return (M)this;
	}
	
	public java.lang.String getHelpName() {
		return getStr("helpName");
	}

	public M setLink(java.lang.String link) {
		set("link", link);
		return (M)this;
	}
	
	public java.lang.String getLink() {
		return getStr("link");
	}

	public M setHelpAnswer(java.lang.String helpAnswer) {
		set("helpAnswer", helpAnswer);
		return (M)this;
	}
	
	public java.lang.String getHelpAnswer() {
		return getStr("helpAnswer");
	}

}
